"""
Simple JSON-based file storage for JoMaDe application.
This replaces the complex markdown-based storage with a simpler JSON approach.
"""

import json
import os
import hashlib
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import openai
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class JsonStore:
    """A simple JSON file-based data store."""
    
    def __init__(self, filename: str):
        """
        Initialize the JSON store.
        
        Args:
            filename: Path to the JSON file
        """
        self.filename = filename
        self.data = []
        
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(filename), exist_ok=True)
        
        # Load data from file or create empty file
        self.load()
    
    def load(self) -> None:
        """Load data from the JSON file."""
        try:
            if os.path.exists(self.filename):
                with open(self.filename, 'r', encoding='utf-8') as f:
                    self.data = json.load(f)
                logger.info(f"Loaded {len(self.data)} items from {self.filename}")
            else:
                self.data = []
                self.save()  # Create the file
                logger.info(f"Created new data file: {self.filename}")
        except Exception as e:
            logger.error(f"Error loading data from {self.filename}: {str(e)}")
            self.data = []
            self.save()  # Create the file with empty data
    
    def save(self) -> None:
        """Save data to the JSON file."""
        try:
            with open(self.filename, 'w', encoding='utf-8') as f:
                json.dump(self.data, f, indent=2)
            logger.info(f"Saved {len(self.data)} items to {self.filename}")
        except Exception as e:
            logger.error(f"Error saving data to {self.filename}: {str(e)}")
    
    def get_all(self) -> List[Dict[str, Any]]:
        """Get all items from the store."""
        return self.data
    
    def get_by_id(self, item_id: str) -> Optional[Dict[str, Any]]:
        """Get an item by its ID."""
        for item in self.data:
            if item.get('id') == item_id:
                return item
        return None
    
    def add(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """
        Add a new item to the store.
        
        Args:
            item: The item to add
            
        Returns:
            The added item with generated ID if not provided
        """
        # Generate ID if not provided
        if 'id' not in item:
            item['id'] = str(len(self.data) + 1)
        
        # Add timestamps
        now = datetime.now().isoformat()
        item['created_at'] = now
        item['updated_at'] = now
        
        self.data.append(item)
        self.save()
        return item
    
    def update(self, item_id: str, updates: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Update an existing item.
        
        Args:
            item_id: ID of the item to update
            updates: Dictionary of fields to update
            
        Returns:
            The updated item or None if not found
        """
        for i, item in enumerate(self.data):
            if item.get('id') == item_id:
                # Update the item
                self.data[i].update(updates)
                # Update timestamp
                self.data[i]['updated_at'] = datetime.now().isoformat()
                self.save()
                return self.data[i]
        return None
    
    def delete(self, item_id: str) -> bool:
        """
        Delete an item by ID.
        
        Args:
            item_id: ID of the item to delete
            
        Returns:
            True if deleted, False if not found
        """
        for i, item in enumerate(self.data):
            if item.get('id') == item_id:
                self.data.pop(i)
                self.save()
                return True
        return False
    
    def clear(self) -> None:
        """Clear all data from the store."""
        self.data = []
        self.save()


# Specialized stores for specific data types

class JobUrlStore(JsonStore):
    """Store for job URLs with additional functionality."""

    def add(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """Add a job URL with duplicate prevention and proper prefix generation."""
        url = item.get('url', '').strip()
        if not url:
            raise ValueError("URL is required")

        # Check for duplicate URLs
        existing_url = self.find_by_url(url)
        if existing_url:
            logger.warning(f"URL already exists with prefix {existing_url.get('prefix')}: {url}")
            return existing_url

        # Generate next available prefix
        prefix = self._generate_next_prefix()

        # Extract domain name for display
        domain_name = self._extract_domain_name(url)

        # Set required fields (remove updated_at, keep only created_at)
        item['url'] = url
        item['prefix'] = prefix
        item.setdefault('is_active', True)
        item.setdefault('name', domain_name)  # Use domain name instead of generic "Job Source"
        item.setdefault('include_in_scraping', True)  # New field for scraping control
        item.setdefault('last_scraped', None)  # Track when URL was last scraped

        # Remove updated_at field - we only need created_at
        if 'updated_at' in item:
            del item['updated_at']

        return super().add(item)

    def find_by_url(self, url: str) -> Optional[Dict[str, Any]]:
        """Find a URL entry by its URL string."""
        url = url.strip()
        for item in self.data:
            if item.get('url', '').strip() == url:
                return item
        return None

    def find_by_prefix(self, prefix: str) -> Optional[Dict[str, Any]]:
        """Find a URL entry by its prefix."""
        for item in self.data:
            if item.get('prefix') == prefix:
                return item
        return None

    def _generate_next_prefix(self) -> str:
        """Generate the next available prefix in sequence (AAA, AAB, AAC, etc.)."""
        existing_prefixes = {item.get('prefix') for item in self.data if item.get('prefix')}

        # Start with AAA and find the next available prefix
        index = 0
        while True:
            prefix = self._generate_prefix(index)
            if prefix not in existing_prefixes:
                return prefix
            index += 1

            # Safety check to prevent infinite loop
            if index > 17576:  # 26^3 = 17576 possible 3-letter combinations
                raise ValueError("No more available prefixes")

    def _generate_prefix(self, index: int) -> str:
        """Generate a three-letter prefix (AAA, AAB, AAC, etc.) from an index."""
        # Ensure index is positive
        index = max(0, index)

        # Convert to base-26 (A-Z) with 3 digits
        first_char = chr(65 + (index // 676) % 26)  # 26^2 = 676
        second_char = chr(65 + (index // 26) % 26)
        third_char = chr(65 + index % 26)

        return f"{first_char}{second_char}{third_char}"

    def _extract_domain_name(self, url: str) -> str:
        """Extract domain name from URL for display purposes."""
        try:
            from urllib.parse import urlparse
            parsed_url = urlparse(url)
            domain_parts = parsed_url.hostname.split('.')
            if len(domain_parts) >= 2:
                # Get the main domain name (second-to-last part)
                # e.g., "hapeko" from "www.hapeko.de"
                return domain_parts[-2]
            else:
                return parsed_url.hostname or 'unknown'
        except Exception:
            return 'unknown'

    def deduplicate(self) -> int:
        """Remove duplicate URLs, keeping the first occurrence of each unique URL."""
        seen_urls = set()
        unique_items = []
        removed_count = 0

        for item in self.data:
            url = item.get('url', '').strip()
            if url and url not in seen_urls:
                seen_urls.add(url)
                # Clean up the item: remove updated_at, ensure proper structure
                clean_item = {
                    'url': url,
                    'prefix': item.get('prefix', ''),
                    'is_active': item.get('is_active', True),
                    'name': item.get('name', f"Job Source {item.get('prefix', '')}"),
                    'include_in_scraping': item.get('include_in_scraping', True),
                    'last_scraped': item.get('last_scraped', None),
                    'created_at': item.get('created_at', datetime.now().isoformat())
                }
                # Remove id field - use prefix as unique identifier
                if 'id' in item:
                    clean_item['id'] = item['id']
                unique_items.append(clean_item)
            else:
                removed_count += 1

        # Update data and save
        self.data = unique_items
        self.save()

        logger.info(f"Deduplication complete: removed {removed_count} duplicates, kept {len(unique_items)} unique URLs")
        return removed_count

    def normalize_prefixes(self) -> int:
        """Reassign prefixes in sequential order (AAA, AAB, AAC, etc.) and clean up structure."""
        updated_count = 0

        # Sort by creation date to maintain some consistency
        sorted_items = sorted(self.data, key=lambda x: x.get('created_at', ''))

        for index, item in enumerate(sorted_items):
            new_prefix = self._generate_prefix(index)
            old_prefix = item.get('prefix', '')

            # Clean up the item structure with domain name
            url = item.get('url', '').strip()
            domain_name = self._extract_domain_name(url)

            clean_item = {
                'url': url,
                'prefix': new_prefix,
                'is_active': item.get('is_active', True),
                'name': domain_name,  # Use domain name instead of generic name
                'include_in_scraping': item.get('include_in_scraping', True),
                'last_scraped': item.get('last_scraped', None),
                'created_at': item.get('created_at', datetime.now().isoformat())
            }

            # Update the item
            sorted_items[index] = clean_item

            if old_prefix != new_prefix:
                updated_count += 1
                logger.info(f"Updated prefix: {old_prefix} -> {new_prefix} for {clean_item['url']}")

        # Update data and save
        self.data = sorted_items
        self.save()

        logger.info(f"Prefix normalization complete: updated {updated_count} prefixes")
        return updated_count

    def update_include_in_scraping(self, prefix: str, include: bool) -> bool:
        """Update the include_in_scraping flag for a URL by prefix."""
        for item in self.data:
            if item.get('prefix') == prefix:
                item['include_in_scraping'] = include
                self.save()
                return True
        return False

    def update_all_include_in_scraping(self, include: bool) -> int:
        """Update the include_in_scraping flag for all URLs."""
        updated_count = 0
        for item in self.data:
            if item.get('include_in_scraping') != include:
                item['include_in_scraping'] = include
                updated_count += 1

        if updated_count > 0:
            self.save()
            logger.info(f"Updated include_in_scraping to {include} for {updated_count} URLs")

        return updated_count

    def update_last_scraped(self, prefix: str, timestamp: str = None) -> bool:
        """Update the last_scraped timestamp for a URL by prefix."""
        if timestamp is None:
            timestamp = datetime.now().isoformat()

        for item in self.data:
            if item.get('prefix') == prefix:
                item['last_scraped'] = timestamp
                self.save()
                return True
        return False

    def get_filtered_urls(self, min_age_days: int = 1, force_scrape: bool = False) -> List[Dict[str, Any]]:
        """
        Get URLs filtered by age and include_in_scraping flag.

        Args:
            min_age_days: Minimum age in days since last scrape (0 = all URLs)
            force_scrape: If True, ignore age filter but still respect include_in_scraping

        Returns:
            List of URLs that should be scraped
        """
        from datetime import datetime, timedelta

        filtered_urls = []
        now = datetime.now()
        min_age_threshold = now - timedelta(days=min_age_days)

        for item in self.data:
            # Skip if not active or not included in scraping
            if not item.get('is_active', True) or not item.get('include_in_scraping', True):
                continue

            # If force_scrape, include all active URLs that are marked for scraping
            if force_scrape:
                filtered_urls.append(item)
                continue

            # Check age filter
            last_scraped = item.get('last_scraped')
            if last_scraped is None:
                # Never scraped, include it
                filtered_urls.append(item)
            else:
                try:
                    last_scraped_dt = datetime.fromisoformat(last_scraped.replace('Z', '+00:00'))
                    if last_scraped_dt <= min_age_threshold:
                        filtered_urls.append(item)
                except (ValueError, AttributeError):
                    # Invalid timestamp, treat as never scraped
                    filtered_urls.append(item)

        return filtered_urls

    def get_filter_stats(self, min_age_days: int = 1) -> Dict[str, int]:
        """Get statistics about URL filtering."""
        total_urls = len([item for item in self.data if item.get('is_active', True)])
        disabled_urls = len([item for item in self.data if item.get('is_active', True) and not item.get('include_in_scraping', True)])

        from datetime import datetime, timedelta
        now = datetime.now()
        min_age_threshold = now - timedelta(days=min_age_days)

        too_recent_urls = 0
        for item in self.data:
            if not item.get('is_active', True) or not item.get('include_in_scraping', True):
                continue

            last_scraped = item.get('last_scraped')
            if last_scraped:
                try:
                    last_scraped_dt = datetime.fromisoformat(last_scraped.replace('Z', '+00:00'))
                    if last_scraped_dt > min_age_threshold:
                        too_recent_urls += 1
                except (ValueError, AttributeError):
                    pass

        eligible_urls = total_urls - disabled_urls - too_recent_urls

        return {
            'total_urls': total_urls,
            'eligible_urls': max(0, eligible_urls),
            'disabled_urls': disabled_urls,
            'too_recent_urls': too_recent_urls
        }


class CVStore(JsonStore):
    """Store for CV data with additional functionality."""
    
    def get_summary(self) -> str:
        """Get the CV summary text."""
        if not self.data:
            return ""
        return self.data[0].get('summary', '')
    
    def update_summary(self, summary: str) -> Dict[str, Any]:
        """Update the CV summary."""
        if not self.data:
            # Create new CV entry
            return self.add({'summary': summary})
        
        # Update existing entry
        return self.update(self.data[0]['id'], {'summary': summary})


class ShortlistStore(JsonStore):
    """Store for shortlisted jobs with metadata and versioning."""

    def add_shortlisted_job(self, job: Dict[str, Any], match_score: float, cv_summary: str, temperature: float = 0.7, total_job_count: int = 0) -> Dict[str, Any]:
        """Add a job to the shortlist with metadata."""
        shortlist_entry = {
            'job_id': job.get('id'),
            'job_data': job.copy(),
            'match_score': round(match_score, 3),
            'confidence_percentage': round(match_score * 100, 1),
            'shortlisted_at': datetime.now().isoformat(),
            'cv_summary_hash': hashlib.md5(cv_summary.encode()).hexdigest()[:8],  # Short hash for CV version tracking
            'temperature_used': temperature,
            'total_job_count_when_created': total_job_count,  # Store original job count for cache validation
            'is_active': True
        }

        # Remove any existing entry for this job
        self.remove_job_from_shortlist(job.get('id'))

        return self.add(shortlist_entry)

    def remove_job_from_shortlist(self, job_id: str) -> bool:
        """Remove a job from the shortlist."""
        removed = False
        self.data = [entry for entry in self.data if entry.get('job_id') != job_id]
        removed = True
        if removed:
            self.save()
        return removed

    def get_active_shortlist(self, min_confidence: float = 75.0) -> List[Dict[str, Any]]:
        """Get active shortlisted jobs above minimum confidence level."""
        active_jobs = []
        for entry in self.data:
            if (entry.get('is_active', True) and
                entry.get('confidence_percentage', 0) >= min_confidence):
                active_jobs.append(entry)

        # Sort by confidence percentage (highest first)
        active_jobs.sort(key=lambda x: x.get('confidence_percentage', 0), reverse=True)
        return active_jobs

    def get_shortlist_statistics(self) -> Dict[str, Any]:
        """Get statistics about the shortlist."""
        total_entries = len(self.data)
        active_entries = len([e for e in self.data if e.get('is_active', True)])

        if not self.data:
            return {
                'total_entries': 0,
                'active_entries': 0,
                'average_confidence': 0,
                'last_updated': None
            }

        active_confidences = [e.get('confidence_percentage', 0) for e in self.data if e.get('is_active', True)]
        avg_confidence = sum(active_confidences) / len(active_confidences) if active_confidences else 0

        # Get most recent shortlisting date
        dates = [e.get('shortlisted_at') for e in self.data if e.get('shortlisted_at')]
        last_updated = max(dates) if dates else None

        return {
            'total_entries': total_entries,
            'active_entries': active_entries,
            'average_confidence': round(avg_confidence, 1),
            'last_updated': last_updated
        }

    def refresh_shortlist_for_cv_change(self, new_cv_summary: str) -> bool:
        """
        Check if CV has changed and refresh shortlist if needed.
        Returns True if refresh was performed.
        """
        if not self.data:
            return False

        # Get current CV hash from most recent entry
        current_cv_hash = self.data[0].get('cv_summary_hash', '') if self.data else ''
        new_cv_hash = hashlib.md5(new_cv_summary.encode()).hexdigest()[:8]

        if current_cv_hash != new_cv_hash:
            logger.info(f"CV changed (hash: {current_cv_hash} -> {new_cv_hash}), refreshing shortlist")
            # Mark all current entries as inactive
            for entry in self.data:
                entry['is_active'] = False
                entry['updated_at'] = datetime.now().isoformat()

            self.save()
            return True

        return False

    def get_cv_change_status(self, current_cv_summary: str) -> Dict[str, Any]:
        """
        Get information about CV changes and shortlist status.
        """
        if not self.data:
            return {
                'has_shortlist': False,
                'cv_changed': False,
                'current_cv_hash': hashlib.md5(current_cv_summary.encode()).hexdigest()[:8],
                'shortlist_cv_hash': None,
                'needs_refresh': False
            }

        current_cv_hash = hashlib.md5(current_cv_summary.encode()).hexdigest()[:8]
        shortlist_cv_hash = self.data[0].get('cv_summary_hash', '') if self.data else ''
        cv_changed = current_cv_hash != shortlist_cv_hash

        return {
            'has_shortlist': len(self.data) > 0,
            'cv_changed': cv_changed,
            'current_cv_hash': current_cv_hash,
            'shortlist_cv_hash': shortlist_cv_hash,
            'needs_refresh': cv_changed and len(self.data) > 0,
            'active_entries': len([e for e in self.data if e.get('is_active', True)]),
            'total_entries': len(self.data)
        }


class ComprehensiveAnalysisStore(JsonStore):
    """Store for comprehensive job analysis results (Stage 2 matching) with caching capabilities."""

    def add_comprehensive_analysis(self, job: Dict[str, Any], stage1_confidence: float,
                                 stage2_confidence: float, pros: List[str], cons: List[str],
                                 cv_summary: str, temperature: float = 0.7) -> Dict[str, Any]:
        """Add a comprehensive analysis result for a job."""
        analysis_entry = {
            'job_id': job.get('id'),
            'job_data': job.copy(),
            'stage1_confidence': round(stage1_confidence, 1),
            'stage2_confidence': round(stage2_confidence, 1),
            'confidence_change': round(stage2_confidence - stage1_confidence, 1),
            'pros': pros,
            'cons': cons,
            'analyzed_at': datetime.now().isoformat(),
            'cv_summary_hash': hashlib.md5(cv_summary.encode()).hexdigest()[:8],
            'temperature_used': temperature,
            'matching_stage': 'comprehensive',
            'is_active': True
        }

        # Remove any existing analysis for this job
        self.remove_analysis_for_job(job.get('id'))

        return self.add(analysis_entry)

    def remove_analysis_for_job(self, job_id: str) -> bool:
        """Remove comprehensive analysis for a specific job."""
        original_count = len(self.data)
        self.data = [entry for entry in self.data if entry.get('job_id') != job_id]
        removed = len(self.data) < original_count
        if removed:
            self.save()
        return removed

    def get_analysis_for_job(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Get comprehensive analysis for a specific job."""
        for entry in self.data:
            if entry.get('job_id') == job_id and entry.get('is_active', True):
                return entry
        return None

    def get_all_analyses(self) -> List[Dict[str, Any]]:
        """Get all active comprehensive analyses."""
        return [entry for entry in self.data if entry.get('is_active', True)]

    def get_cached_analysis(self, job_ids: List[str], cv_summary: str, temperature: float,
                          cache_expiry_hours: int = 24) -> Dict[str, Dict[str, Any]]:
        """
        Get cached comprehensive analyses for given job IDs if they exist and are valid.

        Returns:
            Dict mapping job_id to analysis data for jobs that have valid cached results
        """
        if not job_ids:
            return {}

        current_cv_hash = hashlib.md5(cv_summary.encode()).hexdigest()[:8]
        cache_cutoff = datetime.now() - timedelta(hours=cache_expiry_hours)
        cached_results = {}

        for entry in self.data:
            if not entry.get('is_active', True):
                continue

            job_id = entry.get('job_id')
            if job_id not in job_ids:
                continue

            # Check CV hash match
            if entry.get('cv_summary_hash') != current_cv_hash:
                continue

            # Check temperature match (allow small tolerance)
            entry_temp = entry.get('temperature_used', 0.7)
            if abs(entry_temp - temperature) > 0.05:
                continue

            # Check cache expiry
            try:
                analyzed_at = datetime.fromisoformat(entry.get('analyzed_at', ''))
                if analyzed_at < cache_cutoff:
                    continue
            except Exception:
                continue

            cached_results[job_id] = entry

        return cached_results

    def get_analysis_statistics(self) -> Dict[str, Any]:
        """Get statistics about comprehensive analyses."""
        active_analyses = [e for e in self.data if e.get('is_active', True)]

        if not active_analyses:
            return {
                'total_analyses': 0,
                'avg_stage2_confidence': 0,
                'avg_confidence_change': 0,
                'positive_changes': 0,
                'negative_changes': 0,
                'last_analyzed': None
            }

        stage2_confidences = [e.get('stage2_confidence', 0) for e in active_analyses]
        confidence_changes = [e.get('confidence_change', 0) for e in active_analyses]

        positive_changes = len([c for c in confidence_changes if c > 0])
        negative_changes = len([c for c in confidence_changes if c < 0])

        # Get most recent analysis date
        dates = [e.get('analyzed_at') for e in active_analyses if e.get('analyzed_at')]
        last_analyzed = max(dates) if dates else None

        return {
            'total_analyses': len(active_analyses),
            'avg_stage2_confidence': round(sum(stage2_confidences) / len(stage2_confidences), 1),
            'avg_confidence_change': round(sum(confidence_changes) / len(confidence_changes), 1),
            'positive_changes': positive_changes,
            'negative_changes': negative_changes,
            'last_analyzed': last_analyzed
        }


class JobStore(JsonStore):
    """Store for job data with additional functionality."""

    def __init__(self, filename: str):
        """Initialize JobStore with OpenAI client for LLM-based matching."""
        super().__init__(filename)

        # Initialize OpenAI client for LLM-based job matching
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        if self.openai_api_key:
            self.openai_client = openai.OpenAI(api_key=self.openai_api_key)
        else:
            logger.warning("OPENAI_API_KEY not found - LLM-based matching will be disabled")
            self.openai_client = None

    def get_by_source(self, source_prefix: str) -> List[Dict[str, Any]]:
        """Get jobs by source prefix."""
        return [job for job in self.data if job.get('source') == source_prefix]

    def get_source_statistics(self) -> Dict[str, Dict[str, Any]]:
        """Get statistics for each source URL."""
        # First, get URL to prefix mapping using proper path resolution
        import os
        data_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data")
        url_store = JobUrlStore(os.path.join(data_dir, "job_urls.json"))
        url_to_prefix = {}
        prefix_to_url = {}

        for url_data in url_store.data:
            url = url_data.get('url')
            prefix = url_data.get('prefix')
            if url and prefix:
                url_to_prefix[url] = prefix
                prefix_to_url[prefix] = url

        # Initialize stats for each URL
        stats = {}
        for url in url_to_prefix.keys():
            stats[url] = {
                'scraped_jobs': 0,
                'shortlisted_jobs': 0,
                'last_scraped': None
            }

        # Count only scraped jobs by source prefix and map to URLs
        # Since we cleaned the database, all jobs are now scraped jobs
        for job in self.data:
            source_prefix = job.get('source', 'Unknown')
            url = prefix_to_url.get(source_prefix)

            if url and url in stats:
                # All jobs in the cleaned database have scraped_at field
                stats[url]['scraped_jobs'] += 1

                # Update last scraped time
                scraped_at = job.get('scraped_at')
                if scraped_at and (not stats[url]['last_scraped'] or scraped_at > stats[url]['last_scraped']):
                    stats[url]['last_scraped'] = scraped_at

                # Check if shortlisted
                if job.get('isShortlisted', False):
                    stats[url]['shortlisted_jobs'] += 1

        return stats

    def calculate_llm_title_match_score(self, job_title: str, cv_summary: str, temperature: float = 0.7) -> float:
        """
        Use LLM to calculate match score between job title and CV summary.
        Returns a score between 0.0 and 1.0.
        """
        if not self.openai_client:
            logger.warning("OpenAI client not available - falling back to keyword matching")
            return 0.0

        if not job_title or not cv_summary:
            return 0.0

        try:
            prompt = f"""
You are an expert job matching system. Compare this job title against the candidate's CV summary and provide a match score.

Job Title: "{job_title}"

CV Summary: "{cv_summary}"

Instructions:
1. Analyze how well the job title matches the candidate's experience, skills, and career level
2. Consider industry alignment, role seniority, and skill requirements
3. Return ONLY a decimal number between 0.0 and 1.0 where:
   - 0.0-0.3: Poor match (different field/level)
   - 0.4-0.6: Moderate match (some overlap)
   - 0.7-0.8: Good match (strong alignment)
   - 0.9-1.0: Excellent match (perfect fit)

Return only the numeric score, no explanation.
"""

            response = self.openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": "You are a precise job matching expert. Return only numeric scores between 0.0 and 1.0."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=10,
                temperature=temperature
            )

            score_text = response.choices[0].message.content.strip()
            score = float(score_text)

            # Ensure score is within valid range
            score = max(0.0, min(1.0, score))

            logger.info(f"LLM Title Match: '{job_title[:50]}...' -> Score: {score}")
            return score

        except Exception as e:
            logger.error(f"Error in LLM title matching: {str(e)}")
            return 0.0

    def batch_llm_title_matching(self, jobs: List[Dict[str, Any]], cv_summary: str, temperature: float = 0.7) -> List[Dict[str, Any]]:
        """
        Perform efficient batch LLM-based title matching for multiple jobs.
        Processes multiple jobs in a single LLM call to reduce token costs and improve speed.

        Args:
            jobs: List of job dictionaries to score
            cv_summary: CV summary text for matching
            temperature: LLM temperature setting (0.0-1.0) for creativity/consistency

        Returns:
            List of jobs with added 'llm_title_score' and 'confidence_percentage' fields.
        """
        if not self.openai_client:
            logger.warning("OpenAI client not available - skipping LLM matching")
            return []

        if not cv_summary:
            logger.warning("No CV summary provided for LLM matching")
            return []

        logger.info(f"🤖 Starting efficient LLM batch title matching for {len(jobs)} jobs...")

        scored_jobs = []
        batch_size = 20  # Process 20 jobs per LLM call for efficiency

        for i in range(0, len(jobs), batch_size):
            batch = jobs[i:i + batch_size]
            logger.info(f"🔍 Processing batch {i//batch_size + 1}/{(len(jobs) + batch_size - 1)//batch_size} ({len(batch)} jobs in single LLM call)")

            # Get scores for entire batch in one LLM call
            batch_scores = self.calculate_batch_llm_title_scores(batch, cv_summary, temperature)

            # Apply scores to jobs
            for j, job in enumerate(batch):
                if j < len(batch_scores) and batch_scores[j] > 0.0:
                    job_copy = job.copy()
                    job_copy['llm_title_score'] = float(round(batch_scores[j], 3))
                    job_copy['confidence_percentage'] = float(round(batch_scores[j] * 100, 1))
                    job_copy['match_score'] = float(batch_scores[j])  # For compatibility
                    job_copy['isShortlisted'] = True
                    job_copy['matching_stage'] = 'title_llm_batch'  # Track which stage this is from
                    scored_jobs.append(job_copy)

        logger.info(f"✅ LLM Batch Title Matching Complete: {len(scored_jobs)} jobs with scores > 0.0")
        return scored_jobs

    def calculate_batch_llm_title_scores(self, jobs: List[Dict[str, Any]], cv_summary: str, temperature: float = 0.7) -> List[float]:
        """
        Calculate match scores for multiple job titles in a single LLM call.
        Much more efficient than individual calls.
        Returns list of scores in same order as input jobs.
        """
        if not jobs:
            return []

        try:
            # Build job list for the prompt
            job_list = []
            for i, job in enumerate(jobs):
                title = job.get('title', '').strip()
                if title:
                    job_list.append(f"{i+1}. {title}")

            if not job_list:
                return [0.0] * len(jobs)

            prompt = f"""
You are an expert job matching system. Compare these job titles against the candidate's CV summary and provide match scores.

CV Summary: "{cv_summary}"

Job Titles to Score:
{chr(10).join(job_list)}

Instructions:
1. For each job title, analyze how well it matches the candidate's experience, skills, and career level
2. Consider industry alignment, role seniority, and skill requirements
3. Return ONLY a JSON array of decimal scores between 0.0 and 1.0 where:
   - 0.0-0.3: Poor match (different field/level)
   - 0.4-0.6: Moderate match (some overlap)
   - 0.7-0.8: Good match (strong alignment)
   - 0.9-1.0: Excellent match (perfect fit)

Return format: [0.7, 0.3, 0.8, 0.5, ...]
Return exactly {len(job_list)} scores in the same order as the job titles.
"""

            response = self.openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": "You are a precise job matching expert. Return only JSON arrays of numeric scores between 0.0 and 1.0."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=200,  # Enough for score array
                temperature=temperature
            )

            response_text = response.choices[0].message.content.strip()
            logger.info(f"🤖 LLM Batch Response: {response_text}")

            # Parse JSON array of scores
            import json
            scores = json.loads(response_text)

            # Ensure we have the right number of scores
            if len(scores) != len(job_list):
                logger.warning(f"LLM returned {len(scores)} scores but expected {len(job_list)}")
                # Pad or truncate to match
                while len(scores) < len(job_list):
                    scores.append(0.0)
                scores = scores[:len(job_list)]

            # Ensure all scores are valid floats between 0.0 and 1.0
            validated_scores = []
            for i, score in enumerate(scores):
                try:
                    float_score = float(score)
                    validated_score = max(0.0, min(1.0, float_score))
                    validated_scores.append(validated_score)
                    if validated_score > 0.0:
                        logger.info(f"📊 Job {i+1}: '{jobs[i].get('title', '')[:50]}...' -> Score: {validated_score}")
                except (ValueError, TypeError):
                    logger.warning(f"Invalid score for job {i+1}: {score}")
                    validated_scores.append(0.0)

            # Pad with zeros if we have fewer jobs than expected
            while len(validated_scores) < len(jobs):
                validated_scores.append(0.0)

            return validated_scores[:len(jobs)]

        except Exception as e:
            logger.error(f"Error in batch LLM title matching: {str(e)}")
            return [0.0] * len(jobs)

    def calculate_job_match_score(self, job: Dict[str, Any], cv_data: Dict[str, Any]) -> float:
        """Calculate match score between job and CV (0.0 to 1.0)."""
        if not cv_data:
            return 0.0

        job_title = job.get('title', '').lower()
        job_description = job.get('description', '').lower()
        job_location = job.get('location', '').lower()

        # Get CV content - our CV data has only a summary field
        cv_summary = cv_data.get('summary', '').lower()
        if not cv_summary:
            return 0.0

        # Extract key terms from CV summary
        cv_words = set(cv_summary.split())

        # Define executive/management keywords that should score highly
        executive_keywords = {
            'executive', 'senior', 'director', 'manager', 'head', 'chief', 'ceo', 'cfo', 'coo',
            'vice', 'president', 'vp', 'lead', 'leader', 'management', 'strategic', 'business',
            'development', 'sales', 'account', 'key', 'international', 'global'
        }

        # Define industry/skill keywords from CV
        cv_skills = {
            'supply', 'chain', 'manufacturing', 'industrial', 'business', 'development',
            'sales', 'strategic', 'turnaround', 'restructuring', 'international', 'global',
            'revenue', 'budget', 'team', 'leadership', 'project', 'management', 'sap', 'crm',
            'lean', 'financial', 'operations', 'acquisition', 'trading'
        }

        score = 0.0

        # Title matching (50% weight) - Most important for executive roles
        title_words = set(job_title.split())

        # High score for executive titles
        executive_title_match = len(title_words & executive_keywords)
        if executive_title_match > 0:
            score += 0.3 * min(executive_title_match / 2, 1.0)  # Cap at 2 keywords

        # Medium score for skill/industry matches in title
        skill_title_match = len(title_words & cv_skills)
        if skill_title_match > 0:
            score += 0.2 * min(skill_title_match / 3, 1.0)  # Cap at 3 keywords

        # Description matching (30% weight)
        if job_description and job_description != 'not specified':
            desc_words = set(job_description.split())

            # Executive keywords in description
            exec_desc_match = len(desc_words & executive_keywords)
            if exec_desc_match > 0:
                score += 0.15 * min(exec_desc_match / 5, 1.0)  # Cap at 5 keywords

            # Skill keywords in description
            skill_desc_match = len(desc_words & cv_skills)
            if skill_desc_match > 0:
                score += 0.15 * min(skill_desc_match / 8, 1.0)  # Cap at 8 keywords

        # General word overlap (20% weight) - Broader matching
        all_job_words = title_words | set(job_description.split()) if job_description else title_words
        common_words = cv_words & all_job_words
        # Filter out very common words
        meaningful_words = common_words - {'the', 'and', 'or', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'a', 'an', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'must'}

        if meaningful_words and cv_words:
            overlap_ratio = len(meaningful_words) / min(len(cv_words), 50)  # Normalize against CV length (max 50 words)
            score += 0.2 * min(overlap_ratio, 1.0)

        # Ensure score is between 0 and 1
        return min(score, 1.0)

    def _normalize_title(self, title: str) -> str:
        """Normalize job title for better duplicate detection."""
        import re

        # Convert to lowercase and strip
        normalized = title.lower().strip()

        # Remove common variations and formatting
        normalized = re.sub(r'\s*\(m/w/d\)\s*', '', normalized)  # Remove (m/w/d)
        normalized = re.sub(r'\s*\(all genders\)\s*', '', normalized)  # Remove (all genders)
        normalized = re.sub(r'\s*\(w/m/d\)\s*', '', normalized)  # Remove (w/m/d)
        normalized = re.sub(r'\s+', ' ', normalized)  # Normalize whitespace
        normalized = normalized.strip()

        return normalized

    def _deduplicate_jobs(self, jobs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Remove duplicate jobs based on normalized title and source URL.
        Keeps the job with the highest match score if duplicates exist.
        """
        seen_jobs = {}
        unique_jobs = []

        for job in jobs:
            # Create a unique key based on normalized title and source (more reliable than company name)
            title = self._normalize_title(job.get('title', ''))
            source = job.get('source', '').strip()

            # Create composite key for deduplication - use title + source for better accuracy
            job_key = f"{title}|{source}"

            if job_key not in seen_jobs:
                seen_jobs[job_key] = job
                unique_jobs.append(job)
            else:
                # If duplicate found, keep the one with higher match score
                existing_job = seen_jobs[job_key]
                current_score = job.get('match_score', 0)
                existing_score = existing_job.get('match_score', 0)

                if current_score > existing_score:
                    # Replace with higher scoring job
                    seen_jobs[job_key] = job
                    # Find and replace in unique_jobs list
                    for i, unique_job in enumerate(unique_jobs):
                        if unique_job is existing_job:
                            unique_jobs[i] = job
                            break

                logger.info(f"Removed duplicate job: '{job.get('title')}' from source {job.get('source')} (kept score: {max(current_score, existing_score)})")

        return unique_jobs

    def shortlist_jobs_by_cv(self, cv_data: Dict[str, Any], temp: float = 0.7, min_confidence: float = 75.0, force_refresh: bool = False) -> List[Dict[str, Any]]:
        """
        Shortlist jobs based on CV match using LLM with dynamic temperature and intelligent caching.

        Args:
            cv_data: CV information for matching
            temp: LLM temperature setting (0.0-1.0) - controls creativity/consistency of scoring
            min_confidence: Minimum confidence percentage to display (filters results)
            force_refresh: If True, bypass cache and force new LLM processing
        """
        if not cv_data:
            logger.warning("No CV data provided for shortlisting")
            return []

        # Get only real scraped jobs (exclude mock jobs)
        scraped_jobs = self.get_scraped_jobs()
        if not scraped_jobs:
            logger.warning("No scraped jobs found for shortlisting")
            return []

        cv_summary = cv_data.get('summary', '')
        if not cv_summary:
            logger.warning("No CV summary found for matching")
            return []

        # Check if we can use cached results
        if not force_refresh:
            cached_results = self._get_cached_shortlist(cv_summary, temp, len(scraped_jobs))
            if cached_results is not None:
                # Filter cached results by current min_confidence
                filtered_cached = [job for job in cached_results if job.get('confidence_percentage', 0) >= min_confidence]
                logger.info(f"🚀 Using cached shortlist: {len(filtered_cached)} jobs displayed (from {len(cached_results)} cached, confidence ≥{min_confidence}%)")
                return filtered_cached

        logger.info(f"🎯 Starting fresh LLM-based job shortlisting for {len(scraped_jobs)} scraped jobs")

        # Use LLM-based title matching with dynamic temperature
        scored_jobs = self.batch_llm_title_matching(scraped_jobs, cv_summary, temp)

        if not scored_jobs:
            logger.warning("No jobs scored by LLM title matching")
            return []

        # Remove duplicates before filtering
        unique_jobs = self._deduplicate_jobs(scored_jobs)
        logger.info(f"🔄 After deduplication: {len(unique_jobs)} unique jobs")

        # Sort by LLM score (highest first)
        unique_jobs.sort(key=lambda x: x.get('llm_title_score', 0), reverse=True)

        # Filter by minimum confidence percentage only
        shortlisted = [job for job in unique_jobs if job.get('confidence_percentage', 0) >= min_confidence]
        logger.info(f"✅ After confidence filter ({min_confidence}%): {len(shortlisted)} jobs displayed")

        # Persist shortlisted jobs to dedicated shortlist store
        self._persist_shortlisted_jobs(shortlisted, cv_summary, temp, len(scraped_jobs))

        logger.info(f"🌟 SHORTLISTING COMPLETE: {len(shortlisted)} jobs shortlisted from {len(scraped_jobs)} total (LLM temp: {temp}, min_confidence: {min_confidence}%)")
        return shortlisted

    def _get_cached_shortlist(self, cv_summary: str, temperature: float, current_job_count: int, cache_expiry_hours: int = 24) -> List[Dict[str, Any]] | None:
        """
        Check if we have valid cached shortlist results.

        Args:
            cv_summary: Current CV summary for hash comparison
            temperature: Current temperature setting
            current_job_count: Current number of scraped jobs
            cache_expiry_hours: Hours after which cache expires (default: 24)

        Returns:
            List of cached job results if valid cache exists, None otherwise
        """
        try:
            # Import here to avoid circular imports
            import os
            from datetime import datetime, timedelta

            data_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data")
            shortlist_store = ShortlistStore(os.path.join(data_dir, "shortlist.json"))

            if not shortlist_store.data:
                logger.info("📭 No cached shortlist found")
                return None

            # Get the most recent cache entry to check metadata
            latest_entry = max(shortlist_store.data, key=lambda x: x.get('shortlisted_at', ''))

            # Check CV hash
            current_cv_hash = hashlib.md5(cv_summary.encode()).hexdigest()[:8]
            cached_cv_hash = latest_entry.get('cv_summary_hash', '')
            if current_cv_hash != cached_cv_hash:
                logger.info(f"🔄 CV changed (hash: {cached_cv_hash} → {current_cv_hash}), cache invalid")
                return None

            # Check temperature
            cached_temp = latest_entry.get('temperature_used', 0)
            if abs(cached_temp - temperature) > 0.001:  # Allow small floating point differences
                logger.info(f"🌡️ Temperature changed ({cached_temp} → {temperature}), cache invalid")
                return None

            # Check cache age
            cache_time = datetime.fromisoformat(latest_entry.get('shortlisted_at', ''))
            expiry_time = cache_time + timedelta(hours=cache_expiry_hours)
            if datetime.now() > expiry_time:
                logger.info(f"⏰ Cache expired (created: {cache_time.strftime('%H:%M:%S')}, expiry: {cache_expiry_hours}h)")
                return None

            # Check if job count changed significantly (±10% threshold)
            cached_job_count = latest_entry.get('total_job_count_when_created', len(shortlist_store.data))
            if abs(current_job_count - cached_job_count) > max(5, current_job_count * 0.1):
                logger.info(f"📊 Job count changed significantly ({cached_job_count} → {current_job_count}), cache invalid")
                return None

            # Cache is valid - return job data
            cached_jobs = [entry['job_data'] for entry in shortlist_store.data if entry.get('is_active', True)]
            logger.info(f"✅ Valid cache found: {len(cached_jobs)} jobs (created: {cache_time.strftime('%H:%M:%S')}, temp: {cached_temp})")
            return cached_jobs

        except Exception as e:
            logger.warning(f"Failed to check cache: {str(e)}")
            return None

    def _persist_shortlisted_jobs(self, shortlisted_jobs: List[Dict[str, Any]], cv_summary: str, temperature: float, total_job_count: int):
        """Persist shortlisted jobs to the dedicated shortlist store."""
        try:
            # Import here to avoid circular imports
            import os
            data_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data")
            shortlist_store = ShortlistStore(os.path.join(data_dir, "shortlist.json"))

            # Clear existing shortlist and add new entries
            shortlist_store.data = []

            for job in shortlisted_jobs:
                shortlist_store.add_shortlisted_job(
                    job=job,
                    match_score=job.get('match_score', 0),
                    cv_summary=cv_summary,
                    temperature=temperature,
                    total_job_count=total_job_count
                )

            logger.info(f"Persisted {len(shortlisted_jobs)} jobs to shortlist.json")

        except Exception as e:
            logger.error(f"Failed to persist shortlisted jobs: {str(e)}")
            # Don't fail the shortlisting process if persistence fails

    def get_shortlisted(self) -> List[Dict[str, Any]]:
        """Get shortlisted jobs."""
        return [job for job in self.data if job.get('isShortlisted', False)]

    def get_scraped_jobs(self) -> List[Dict[str, Any]]:
        """Get only real scraped jobs (not mock jobs)."""
        return [job for job in self.data if 'scraped_at' in job]

    def get_mock_jobs(self) -> List[Dict[str, Any]]:
        """Get only mock jobs (no scraped_at field)."""
        return [job for job in self.data if 'scraped_at' not in job]

    def remove_mock_jobs(self) -> int:
        """Remove all mock jobs from the store."""
        mock_jobs = self.get_mock_jobs()
        mock_count = len(mock_jobs)

        if mock_count > 0:
            # Keep only scraped jobs
            self.data = self.get_scraped_jobs()
            self.save()
            logger.info(f"Removed {mock_count} mock jobs, kept {len(self.data)} real scraped jobs")

        return mock_count

    def get_last_scrape_date(self) -> Optional[str]:
        """Get the date of the last scraping operation (YYYY-MM-DD format)."""
        scraped_jobs = self.get_scraped_jobs()
        if not scraped_jobs:
            return None

        # Find the most recent scraped_at timestamp
        latest_scraped_at = max(job['scraped_at'] for job in scraped_jobs)
        # Extract just the date part (YYYY-MM-DD)
        return latest_scraped_at.split('T')[0]

    def was_scraped_today(self) -> bool:
        """Check if scraping was performed today."""
        last_scrape_date = self.get_last_scrape_date()
        if not last_scrape_date:
            return False

        today = datetime.now().strftime('%Y-%m-%d')
        return last_scrape_date == today

    def find_duplicate_by_title(self, title: str, source_prefix: str = None) -> Optional[Dict[str, Any]]:
        """
        Find a job with the same title (case-insensitive).

        Args:
            title: Job title to search for
            source_prefix: Optional source prefix to limit search

        Returns:
            Existing job with same title or None
        """
        title_lower = title.lower().strip()

        for job in self.data:
            # Skip if source filter is specified and doesn't match
            if source_prefix and job.get('source') != source_prefix:
                continue

            existing_title = job.get('title', '').lower().strip()
            if existing_title == title_lower:
                return job

        return None

    def add_if_not_duplicate(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """
        Add a job only if no duplicate title exists.

        Args:
            item: The job item to add

        Returns:
            The added item, existing duplicate, or None if skipped
        """
        title = item.get('title', '')
        source_prefix = item.get('source', '')

        # Check for duplicate by title
        existing_job = self.find_duplicate_by_title(title, source_prefix)
        if existing_job:
            logger.info(f"Skipping duplicate job: '{title}' (ID: {existing_job.get('id')})")
            return existing_job

        # No duplicate found, add the job
        return self.add(item)

    def get_scraping_stats(self) -> Dict[str, Any]:
        """Get statistics about scraped jobs."""
        scraped_jobs = self.get_scraped_jobs()
        mock_jobs = [job for job in self.data if 'scraped_at' not in job]

        stats = {
            'total_jobs': len(self.data),
            'scraped_jobs': len(scraped_jobs),
            'mock_jobs': len(mock_jobs),
            'last_scrape_date': self.get_last_scrape_date(),
            'was_scraped_today': self.was_scraped_today()
        }

        if scraped_jobs:
            # Count jobs by source
            source_counts = {}
            for job in scraped_jobs:
                source = job.get('source', 'Unknown')
                source_counts[source] = source_counts.get(source, 0) + 1
            stats['jobs_by_source'] = source_counts

        return stats

    def batch_comprehensive_analysis(self, shortlisted_jobs: List[Dict[str, Any]], cv_summary: str,
                                   temperature: float = 0.7) -> List[Dict[str, Any]]:
        """
        Perform comprehensive Stage 2 analysis for shortlisted jobs.
        Compares full job descriptions against CV summary with detailed pros/cons assessment.

        Args:
            shortlisted_jobs: List of jobs from Stage 1 shortlisting
            cv_summary: CV summary text for detailed matching
            temperature: LLM temperature setting

        Returns:
            List of jobs with comprehensive analysis results
        """
        if not self.openai_client:
            logger.warning("OpenAI client not available - skipping comprehensive analysis")
            return []

        if not cv_summary or not shortlisted_jobs:
            logger.warning("No CV summary or shortlisted jobs provided for comprehensive analysis")
            return []

        logger.info(f"🔍 Starting comprehensive Stage 2 analysis for {len(shortlisted_jobs)} shortlisted jobs...")

        analyzed_jobs = []
        batch_size = 10  # Smaller batch size for comprehensive analysis due to longer prompts

        for i in range(0, len(shortlisted_jobs), batch_size):
            batch = shortlisted_jobs[i:i + batch_size]
            logger.info(f"📊 Processing comprehensive batch {i//batch_size + 1}/{(len(shortlisted_jobs) + batch_size - 1)//batch_size} ({len(batch)} jobs)")

            # Get comprehensive analysis for entire batch in one LLM call
            batch_analyses = self.calculate_batch_comprehensive_analysis(batch, cv_summary, temperature)

            # Apply analysis results to jobs
            for j, job in enumerate(batch):
                if j < len(batch_analyses) and batch_analyses[j]:
                    analysis = batch_analyses[j]
                    job_copy = job.copy()

                    # Update confidence and add analysis data
                    job_copy['stage1_confidence'] = job.get('confidence_percentage', 0)
                    job_copy['stage2_confidence'] = analysis.get('confidence', 0)
                    job_copy['confidence_change'] = analysis.get('confidence', 0) - job.get('confidence_percentage', 0)
                    job_copy['pros'] = analysis.get('pros', [])
                    job_copy['cons'] = analysis.get('cons', [])
                    job_copy['comprehensive_analysis'] = True
                    job_copy['analyzed_at'] = datetime.now().isoformat()
                    job_copy['matching_stage'] = 'comprehensive'

                    analyzed_jobs.append(job_copy)

        logger.info(f"✅ Comprehensive Analysis Complete: {len(analyzed_jobs)} jobs analyzed")
        return analyzed_jobs

    def calculate_batch_comprehensive_analysis(self, jobs: List[Dict[str, Any]], cv_summary: str,
                                             temperature: float = 0.7) -> List[Dict[str, Any]]:
        """
        Calculate comprehensive analysis for multiple jobs using STORED JOB DATA ONLY.

        This method works purely with job data that was already scraped and stored,
        using job IDs as the primary key. No URL processing or additional fetching is needed.

        Args:
            jobs: List of job dictionaries with complete stored data (id, title, description, etc.)
            cv_summary: CV summary text for detailed matching
            temperature: LLM temperature setting

        Returns:
            List of detailed analysis with confidence scores and pros/cons for each job
        """
        if not jobs:
            logger.warning("No jobs provided for comprehensive analysis")
            return []

        logger.info(f"DEBUG: OpenAI client available: {self.openai_client is not None}")
        logger.info(f"DEBUG: CV summary length: {len(cv_summary)}")
        logger.info(f"DEBUG: Number of jobs to analyze: {len(jobs)}")

        try:
            # Build job list for the prompt using STORED job data (no URL fetching needed)
            job_list = []
            for i, job in enumerate(jobs):
                job_id = job.get('id', 'Unknown ID')
                title = job.get('title', '').strip()
                description = job.get('description', '').strip()
                location = job.get('location', '').strip()
                company = job.get('company', '').strip()

                job_info = f"{i+1}. Job ID: {job_id}\n   Title: {title}"
                if company:
                    job_info += f"\n   Company: {company}"
                if location:
                    job_info += f"\n   Location: {location}"
                if description:
                    # Use the full stored description (enhanced during scraping)
                    desc_preview = description[:800] + "..." if len(description) > 800 else description
                    job_info += f"\n   Description: {desc_preview}"
                else:
                    job_info += f"\n   Description: No detailed description available"

                job_list.append(job_info)

            if not job_list:
                return [None] * len(jobs)

            prompt = f"""
You are an expert job matching system performing comprehensive analysis. Compare these job postings against the candidate's CV summary and provide detailed analysis.

For each job, analyze:
- Role and responsibilities alignment
- Industry fit and experience match
- Required skills vs candidate skills
- Location considerations
- Career level appropriateness
- Language and cultural fit

CV Summary:
{cv_summary}

Job Postings:
{chr(10).join(job_list)}

For each job, return a JSON object with:
- "confidence": numeric score 0-100 (updated confidence after comprehensive analysis)
- "pros": array of specific positive matches (skills, experience, etc.)
- "cons": array of specific concerns or missing requirements

Return format: [{{"confidence": 85, "pros": ["Strong Python experience match", "Leadership background aligns"], "cons": ["No specific industry experience", "Location may require relocation"]}}, ...]

Return exactly {len(job_list)} analysis objects in the same order as the job postings.
"""

            logger.info(f"DEBUG: Sending request to OpenAI API with temperature {temperature}")
            try:
                response = self.openai_client.chat.completions.create(
                    model="gpt-4o-mini",
                    messages=[
                        {"role": "system", "content": "You are a comprehensive job matching expert. Return only valid JSON arrays with detailed analysis."},
                        {"role": "user", "content": prompt}
                    ],
                    max_tokens=2000,  # More tokens for comprehensive analysis
                    temperature=temperature
                )
                logger.info("DEBUG: OpenAI API request successful")
            except Exception as e:
                logger.error(f"DEBUG: OpenAI API request failed: {str(e)}")
                return [None] * len(jobs)

            response_text = response.choices[0].message.content.strip()
            logger.info(f"LLM Comprehensive Analysis Response: {response_text[:200]}...")

            # Parse JSON response with markdown code block handling
            analyses = []
            try:
                # First try: direct JSON parsing
                analyses = json.loads(response_text)
                logger.info("✅ Direct JSON parsing successful")
            except json.JSONDecodeError:
                try:
                    # Second try: extract JSON array from markdown code blocks
                    import re
                    json_match = re.search(r'```json\s*(\[.*?\])\s*```', response_text, re.DOTALL)
                    if json_match:
                        analyses = json.loads(json_match.group(1))
                        logger.info("✅ Markdown JSON extraction successful")
                    else:
                        # Third try: extract any JSON array from response
                        json_match = re.search(r'\[.*\]', response_text, re.DOTALL)
                        if json_match:
                            analyses = json.loads(json_match.group())
                            logger.info("✅ Regex JSON extraction successful")
                        else:
                            logger.error("❌ No JSON array found in LLM response")
                            logger.error(f"Full response: {response_text}")
                            return [None] * len(jobs)
                except json.JSONDecodeError as e:
                    logger.error(f"❌ JSON parsing failed: {str(e)}")
                    logger.error(f"Full response: {response_text}")
                    return [None] * len(jobs)

            if not isinstance(analyses, list):
                logger.error("LLM response is not a list")
                return [None] * len(jobs)

            # Validate and process analyses
            validated_analyses = []
            for i, analysis in enumerate(analyses):
                if i >= len(jobs):
                    break

                if not isinstance(analysis, dict):
                    logger.warning(f"Invalid analysis format for job {i+1}")
                    validated_analyses.append(None)
                    continue

                # Validate and clean analysis data
                try:
                    confidence = float(analysis.get('confidence', 0))
                    confidence = max(0.0, min(100.0, confidence))  # Clamp to 0-100

                    pros = analysis.get('pros', [])
                    if not isinstance(pros, list):
                        pros = []
                    pros = [str(p).strip() for p in pros if str(p).strip()][:5]  # Limit to 5 pros

                    cons = analysis.get('cons', [])
                    if not isinstance(cons, list):
                        cons = []
                    cons = [str(c).strip() for c in cons if str(c).strip()][:5]  # Limit to 5 cons

                    validated_analysis = {
                        'confidence': confidence,
                        'pros': pros,
                        'cons': cons
                    }

                    validated_analyses.append(validated_analysis)

                    job_title = jobs[i].get('title', '')[:50]
                    logger.info(f"📈 Job {i+1}: '{job_title}...' -> Confidence: {confidence}%, Pros: {len(pros)}, Cons: {len(cons)}")

                except (ValueError, TypeError) as e:
                    logger.warning(f"Error processing analysis for job {i+1}: {e}")
                    validated_analyses.append(None)

            # Pad with None if we have fewer analyses than jobs
            while len(validated_analyses) < len(jobs):
                validated_analyses.append(None)

            return validated_analyses[:len(jobs)]

        except Exception as e:
            logger.error(f"Error in batch comprehensive analysis: {str(e)}")
            return [None] * len(jobs)
